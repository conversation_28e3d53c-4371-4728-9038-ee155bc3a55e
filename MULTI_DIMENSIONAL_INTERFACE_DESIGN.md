# 多维度检索接口设计文档

## 概述

本文档描述了多维度检索增强的元认知智能体接口设计，用于支持未来的多维度NER示例检索功能。

## 设计原则

1. **向后兼容性**：现有功能不受影响
2. **渐进式升级**：可以逐个维度测试和集成  
3. **高度可扩展**：支持任意新维度的添加
4. **配置驱动**：通过配置控制功能开关

## 接口设计

### 1. Tool接口 (schemas.py)

#### 当前状态
- 基础的description和k参数
- 简化的prompt配合Function Call

#### 扩展计划
```python
class RetrieveNERExamplesTool(BaseModel):
    """多维度NER示例检索工具"""
    
    # 基础字段（保持兼容）
    description: str = Field(description="文本特征描述")
    k: int = Field(default=3, description="检索示例数量")
    
    # 多维度字段（可选，未来启用）
    entity_density: Optional[float] = Field(default=None, description="实体密度")
    domain: Optional[str] = Field(default=None, description="领域类型") 
    complexity: Optional[float] = Field(default=None, description="复杂度评分")
    additional_features: Optional[Dict[str, Any]] = Field(default=None, description="扩展维度")
```

### 2. 检索器接口 (example_retriever.py)

#### 当前状态
- simple_retrieve() 方法使用文本相似度
- FAISS向量存储

#### 扩展计划
```python
class MultiDimensionalRetriever:
    """多维度检索器"""
    
    async def retrieve_with_dimensions(
        self, 
        query_text: str,
        query_dimensions: Optional[Dict[str, Any]] = None,
        k: int = 3
    ) -> List[Dict[str, Any]]:
        """基于多维度特征检索"""
        # 当前：使用simple_retrieve()
        # 未来：集成多维度相似度计算
        
    def calculate_dimension_similarity(
        self, 
        query_dims: Dict[str, Any], 
        example_dims: Dict[str, Any]
    ) -> float:
        """计算多维度相似度（预留接口）"""
        
    async def store_example_with_dimensions(
        self, 
        example: Dict[str, Any],
        dimensions: Optional[Dict[str, Any]] = None
    ):
        """存储示例及其多维度特征（预留接口）"""
```

### 3. 元认知智能体接口 (meta_cognitive_agent.py)

#### 当前状态
- _execute_retrieval_stage() 处理Function Call
- 使用description进行检索

#### 扩展计划
```python
async def _execute_retrieval_stage_v2(self, tool_calls: List[Any]) -> List[Any]:
    """执行多维度检索阶段"""
    # 提取多维度参数
    query_dimensions = self._extract_dimensions(arguments)
    
    # 执行多维度检索
    examples = await self.example_retriever.retrieve_with_dimensions(
        query_text=arguments.get("description", ""),
        query_dimensions=query_dimensions,
        k=arguments.get("k", 3)
    )

def _extract_dimensions(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """提取多维度特征"""
    # 从Function Call参数中提取维度信息
```

### 4. 配置接口 (config.py)

#### 扩展计划
```python
'multi_dimensional_config': {
    'enabled': False,  # 功能开关
    'dimension_weights': {
        'text_similarity': 0.6,
        'entity_density': 0.15,
        'domain': 0.15,
        'complexity': 0.1
    },
    'precompute_dimensions': True,
    'dimension_cache_path': './cache/dimensions'
}
```

## 实现路线图

### 阶段1：接口预留（当前）
- [x] 优化现有Tool和prompt
- [ ] 在schemas.py中扩展Tool定义
- [ ] 在example_retriever.py中预留多维度接口
- [ ] 在meta_cognitive_agent.py中预留处理逻辑

### 阶段2：维度计算（未来）
- [ ] 实现实体密度计算
- [ ] 实现领域分类
- [ ] 实现复杂度评分
- [ ] 预计算训练数据的维度特征

### 阶段3：多维度检索（未来）
- [ ] 实现多维度相似度算法
- [ ] 集成权重配置
- [ ] 性能优化和测试

### 阶段4：评估和优化（未来）
- [ ] 在CrossNER数据集上评估
- [ ] 调优维度权重
- [ ] 添加新的维度类型

## 技术要点

### LLM指导原则
- 通过prompt明确指定LLM应该分析哪些维度
- 使用Function Call的Field description指导LLM生成合适的参数
- 保持prompt简洁，让Function Call机制发挥作用

### 扩展性设计
- additional_features字段支持任意新维度
- 配置驱动的权重调整
- 插件化的维度计算方法

### 性能考虑
- 预计算维度特征，避免运行时计算
- 缓存机制减少重复计算
- 可配置的功能开关控制性能影响

## 注意事项

1. **向后兼容**：所有新增字段都是Optional，不影响现有功能
2. **渐进式开发**：可以先实现Tool扩展，后续逐步添加检索逻辑
3. **配置控制**：通过配置文件控制多维度功能的启用/禁用
4. **性能监控**：需要监控多维度检索对性能的影响

## 文件修改清单

- [ ] schemas.py - 扩展RetrieveNERExamplesTool
- [ ] example_retriever.py - 添加多维度检索接口
- [ ] meta_cognitive_agent.py - 添加多维度处理逻辑
- [ ] config.py - 添加多维度配置项
- [ ] 新增：dimension_calculator.py - 维度计算模块（未来）
- [ ] 新增：multi_dim_similarity.py - 多维度相似度计算（未来）
