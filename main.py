#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 APIICL - 元认知智能体NER系统 (三阶段统一处理版本)
阶段1: 统一生成检索请求
阶段2: 统一检索
阶段3: 统一NER
"""

# {{ AURA-X: Fix - 解决OpenMP库冲突警告. Approval: 寸止(ID:1738230403). }}
import os
# 设置环境变量解决OpenMP重复库警告
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
# 可选：限制OpenMP线程数以提高稳定性
os.environ['OMP_NUM_THREADS'] = '1'

import asyncio
import argparse
import json
import gc
import traceback
from typing import Dict, Any, Optional, List, Type
from datetime import datetime

from config import CONFIG, set_dataset, list_available_datasets, get_current_dataset_info, initialize_datasets, get_cache_path
from schemas import RetrieveNERExamplesTool
from pydantic import BaseModel
from utils import async_read_json, async_write_json, safe_cancel_tasks, safe_cleanup_tasks, with_timeout, ProgressManager, setup_logging, print_banner, parse_tool_arguments



# 常量定义
DEFAULT_RETRIEVAL_DESCRIPTION = "general NER examples"


# {{ AURA-X: Remove - 删除重复函数定义，使用utils.py中的统一版本. Approval: 寸止(ID:1738230401). }}
# 已从utils.py导入：async_read_json, async_write_json, setup_logging


# {{ AURA-X: Remove - 删除重复的ProgressManager和print_banner定义. Approval: 寸止(ID:1738230401). }}
# 已从utils.py导入：ProgressManager, print_banner


# {{ AURA-X: Remove - 删除重复函数定义. Approval: 寸止(ID:1738230400). }}
# 已从 utils.py 导入 safe_cancel_tasks 和 safe_cleanup_tasks


# {{ AURA-X: Remove - 删除重复的with_timeout定义. Approval: 寸止(ID:1738230401). }}
# 已从utils.py导入：with_timeout


async def process_and_eval_dataset(max_samples: Optional[int] = None) -> Dict[str, Any]:
    """🎯 三阶段统一处理数据集"""

    # 创建进度管理器
    progress = ProgressManager()

    # 获取当前数据集信息
    current_dataset = get_current_dataset_info()
    dataset_path = current_dataset['path']

    # 检查测试集文件
    test_path = dataset_path.replace('train.json', 'test.json')
    if not os.path.exists(test_path):
        print(f"❌ 测试集文件不存在: {test_path}")
        return {}

    # 阶段0：数据准备和向量库预初始化
    progress.start_stage("📚 阶段0：数据准备和向量库预初始化", 3)

    try:
        test_data = await async_read_json(test_path)
        progress.update_progress(completed=1)
    except Exception as e:
        print(f"❌ 加载测试集失败: {e}")
        return {}

    # 限制样本数量
    if max_samples and max_samples < len(test_data):
        test_data = test_data[:max_samples]
    progress.update_progress(completed=2)

    # 预初始化向量库
    progress.log_message("🔍 预初始化向量库...")
    from example_retriever import ExampleRetriever
    global_retriever = ExampleRetriever()
    vector_ready = await global_retriever.initialize_vector_store()
    if vector_ready:
        progress.log_message("✅ 向量库预初始化成功")
    else:
        progress.log_message("⚠️ 向量库预初始化失败，将使用直接模式")

    progress.update_progress(completed=3)
    progress.finish_stage("数据准备阶段完成")
    
    # 初始化元认知智能体
    from meta_cognitive_agent import get_meta_cognitive_agent
    agent = get_meta_cognitive_agent(global_retriever)
    
    # 阶段1：为每个样本生成检索请求
    progress.start_stage("🧠 阶段1：生成检索请求", len(test_data))

    # 检查缓存
    cache_file = get_cache_path(f"requests_{len(test_data)}")

    if os.path.exists(cache_file):
        progress.log_message("📦 发现检索请求缓存，正在加载...")
        try:
            all_retrieval_requests = await async_read_json(cache_file)
            progress.log_message(f"✅ 从缓存加载了 {len(all_retrieval_requests)} 个检索请求")
            progress.update_progress(completed=len(all_retrieval_requests))
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ 缓存文件损坏，重新生成... 错误: {e}")
            all_retrieval_requests = None
    else:
        all_retrieval_requests = None

    if all_retrieval_requests is None:
        async def generate_single_request(i, sample):
            """{{ AURA-X: Optimize - 添加跳过机制. Approval: 寸止(ID:1738230400). }}
            并发生成单个检索请求 - 添加跳过机制"""
            text = sample.get('text', '')

            try:
                stage1_prompt = agent.build_stage1_prompt(text)
                tools: List[Type[BaseModel]] = [RetrieveNERExamplesTool]
                messages = [{"role": "user", "content": stage1_prompt}]

                response = await agent.model_service.generate_with_tools_async(
                    messages=messages,
                    tools=tools
                )

                if response and hasattr(response, 'tool_calls') and response.tool_calls:
                    for tool_call in response.tool_calls:
                        if tool_call.function.name == "RetrieveNERExamplesTool":
                            arguments = parse_tool_arguments(tool_call.function.arguments)
                            if arguments is None:
                                # 标记为跳过
                                return (i, "SKIP", 0)

                            description = arguments.get("description", DEFAULT_RETRIEVAL_DESCRIPTION)
                            k = arguments.get("k", 3)

                            # 提取并记录多维度参数
                            dimensions = {}
                            if arguments.get("entity_density") is not None:
                                dimensions["entity_density"] = arguments["entity_density"]
                            if arguments.get("boundary_ambiguity") is not None:
                                dimensions["boundary_ambiguity"] = arguments["boundary_ambiguity"]
                            if arguments.get("dependency_depth") is not None:
                                dimensions["dependency_depth"] = arguments["dependency_depth"]
                            if arguments.get("additional_features"):
                                dimensions.update(arguments["additional_features"])

                            if dimensions:
                                print(f"🧠 样本{i}多维度分析: {dimensions}")

                            return (i, description, k, dimensions)

                return (i, DEFAULT_RETRIEVAL_DESCRIPTION, 3, {})

            except Exception as e:
                print(f"⚠️ 样本 {i} 生成检索请求失败: {e}")
                return (i, "SKIP", 0, {})  # 标记失败样本为跳过

        batch_size = CONFIG.get('batch_size', 200)
        batch_delay = CONFIG.get('batch_delay', 1.0)
        all_batch_tasks = []

        for i in range(0, len(test_data), batch_size):
            batch_samples = test_data[i:i+batch_size]
            batch_indices = list(range(i, min(i+batch_size, len(test_data))))



            # 创建批次任务（不等待完成）
            batch_tasks = [
                generate_single_request(idx, sample)
                for idx, sample in zip(batch_indices, batch_samples)
            ]

            # 添加到总任务列表
            all_batch_tasks.extend(batch_tasks)

            # 批次发送间隔（不等待当前批次完成）
            if i + batch_size < len(test_data):
                await asyncio.sleep(batch_delay)



        # 使用as_completed显示实时进度
        all_results = []
        completed_count = 0

        for completed_task in asyncio.as_completed(all_batch_tasks):
            result = await completed_task
            all_results.append(result)
            completed_count += 1
            progress.update_progress(completed=completed_count)

        # 处理所有结果
        all_retrieval_requests = []
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ 任务失败: {result}")
                failed_count += 1
            else:
                all_retrieval_requests.append(result)
                success_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段1完成 - 成功: {len(all_retrieval_requests)}/{len(test_data)}")

        # 按样本ID排序，保持顺序
        all_retrieval_requests.sort(key=lambda x: x[0])

        # 过滤掉失败的请求（SKIP标记）
        valid_requests = [req for req in all_retrieval_requests if req[1] != "SKIP"]
        skipped_count = len(all_retrieval_requests) - len(valid_requests)
        if skipped_count > 0:
            print(f"⚠️ 跳过了 {skipped_count} 个失败的样本")
        all_retrieval_requests = valid_requests

        # 清理不再需要的大型列表，释放内存
        del all_batch_tasks, all_results
        # {{ AURA-X: Fix - 修复gc模块作用域问题. Approval: 寸止(ID:1754055919). }}
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存到缓存
        try:
            await async_write_json(cache_file, all_retrieval_requests)
            print(f"💾 检索请求已缓存到: {cache_file}")
        except Exception as e:
            print(f"⚠️ 缓存保存失败: {e}")
    
    print()
    
    # 阶段2：为每个样本执行检索
    progress.start_stage("🔍 阶段2：执行检索", len(all_retrieval_requests))

    # 检查检索结果缓存
    examples_cache_file = get_cache_path(f"examples_{len(test_data)}")

    if os.path.exists(examples_cache_file):
        progress.log_message("📦 发现检索结果缓存，正在加载...")
        try:
            all_examples = await async_read_json(examples_cache_file)
            # 转换字符串键为整数键
            all_examples = {int(k): v for k, v in all_examples.items()}
            progress.log_message(f"✅ 从缓存加载了 {len(all_examples)} 个检索结果")
            progress.update_progress(completed=len(all_examples))
            progress.finish_stage(f"阶段2完成 - 从缓存加载: {len(all_examples)}")
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ 检索缓存文件损坏，重新检索... 错误: {e}")
            all_examples = None
    else:
        all_examples = None

    if all_examples is None:
        batch_size = CONFIG.get('batch_size', 200)
        batch_delay = CONFIG.get('batch_delay', 1.0)

        @with_timeout(CONFIG.get('timeouts', {}).get('single_task', 180))  # 单任务超时
        async def retrieve_single_example(sample_id, description, k, dimensions):
            """并发检索单个示例 - 添加超时保护和多维度支持"""
            try:
                examples = await agent.example_retriever.retrieve_with_dimensions(
                    query_text=description,
                    query_dimensions=dimensions,
                    k=k
                )
                return (sample_id, examples)
            except Exception as e:
                progress.log_message(f"⚠️ 样本 {sample_id} 检索失败: {e}")
                return (sample_id, [])

        all_examples = {}

        all_retrieval_tasks = []

        for i in range(0, len(all_retrieval_requests), batch_size):
            batch_requests = all_retrieval_requests[i:i+batch_size]

            # 创建批次任务（不等待完成）
            batch_tasks = [
                retrieve_single_example(sample_id, description, k, dimensions)
                for sample_id, description, k, dimensions in batch_requests
            ]

            # 添加到总任务列表
            all_retrieval_tasks.extend(batch_tasks)

            # 批次发送间隔（不等待当前批次完成）
            if i + batch_size < len(all_retrieval_requests):
                await asyncio.sleep(batch_delay)



        # 创建任务对象以便跟踪进度
        tasks = [asyncio.create_task(coro) for coro in all_retrieval_tasks]
        all_results = []
        completed_count = 0

        try:
            # 使用as_completed显示实时进度，统一超时配置
            stage2_timeout = CONFIG.get('timeouts', {}).get('stage2_batch', 900)
            for completed_task in asyncio.as_completed(tasks, timeout=stage2_timeout):
                try:
                    result = await completed_task
                    all_results.append(result)
                except Exception as e:
                    all_results.append(e)

                completed_count += 1
                progress.update_progress(completed=completed_count)

        except asyncio.TimeoutError:
            progress.log_message("⚠️ 阶段2任务超时，使用部分结果继续")
            # 安全取消未完成的任务
            await safe_cancel_tasks(tasks)
            # 使用已完成的结果
            if len(all_results) < len(tasks):
                all_results.extend([Exception("Timeout")] * (len(tasks) - len(all_results)))

        finally:
            # 确保所有任务都被清理
            await safe_cleanup_tasks(tasks)

        # 处理所有结果
        all_examples = {}
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ 检索任务失败: {result}")
                failed_count += 1
            else:
                sample_id, examples = result
                all_examples[sample_id] = examples
                success_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段2完成 - 成功: {len(all_examples)}/{len(all_retrieval_requests)}")

        # 清理不再需要的大型列表，释放内存
        del all_retrieval_tasks, tasks, all_results
        # {{ AURA-X: Fix - 修复gc模块作用域问题. Approval: 寸止(ID:1754055919). }}
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存检索结果到缓存
        try:
            await async_write_json(examples_cache_file, all_examples)
            progress.log_message(f"💾 检索结果已缓存到: {examples_cache_file}")
        except Exception as e:
            progress.log_message(f"⚠️ 检索缓存保存失败: {e}")
    
    print()
    
    # 阶段3：为每个样本执行NER
    progress.start_stage("🎯 阶段3：执行NER", len(test_data))

    # 检查NER结果缓存
    ner_cache_file = get_cache_path(f"ner_results_{len(test_data)}")

    if os.path.exists(ner_cache_file):
        progress.log_message("📦 发现NER结果缓存，正在加载...")
        try:
            cached_results = await async_read_json(ner_cache_file)
            progress.log_message(f"✅ 从缓存加载了 {len(cached_results)} 个NER结果")
            results = cached_results
            progress.update_progress(completed=len(cached_results))
            progress.finish_stage(f"阶段3完成 - 从缓存加载: {len(cached_results)}")
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ NER缓存文件损坏，重新执行NER... 错误: {e}")
            results = None
    else:
        results = None

    if results is None:
        batch_size = CONFIG.get('batch_size', 200)
        batch_delay = CONFIG.get('batch_delay', 1.0)

        async def execute_single_ner(i, sample):
            """并发执行单个NER"""
            text = sample.get('text', '')
            true_labels = sample.get('label', {})
            examples = all_examples.get(i, [])

            try:
                # 执行NER
                predicted_labels = await agent.execute_ner_stage(text, examples)
            except Exception as e:
                print(f"⚠️ 样本 {i} NER失败: {e}")
                predicted_labels = {}

            if not isinstance(predicted_labels, dict):
                predicted_labels = {}

            # 计算指标
            sample_correct = 0
            sample_total = sum(len(entities) for entities in true_labels.values())
            sample_predicted = sum(len(entities) for entities in predicted_labels.values())

            for entity_type, true_entities in true_labels.items():
                predicted_entities_of_type = predicted_labels.get(entity_type, [])
                for entity in true_entities:
                    if entity in predicted_entities_of_type:
                        sample_correct += 1

            return {
                'text': text,
                'true_labels': true_labels,
                'predicted_labels': predicted_labels,
                'correct': sample_correct,
                'total_true': sample_total,
                'total_predicted': sample_predicted
            }

        results = []

        all_ner_tasks = []

        for i in range(0, len(test_data), batch_size):
            batch_samples = test_data[i:i+batch_size]
            batch_indices = list(range(i, min(i+batch_size, len(test_data))))



            # 创建批次任务（不等待完成）
            batch_tasks = [
                execute_single_ner(idx, sample)
                for idx, sample in zip(batch_indices, batch_samples)
            ]

            # 添加到总任务列表
            all_ner_tasks.extend(batch_tasks)

            # 批次发送间隔（不等待当前批次完成）
            if i + batch_size < len(test_data):
                await asyncio.sleep(batch_delay)



        # 创建任务对象以便跟踪进度
        tasks = [asyncio.create_task(coro) for coro in all_ner_tasks]
        all_results = []
        completed_count = 0

        try:
            # 使用as_completed显示实时进度，增加超时时间
            stage3_timeout = CONFIG.get('timeouts', {}).get('stage3_batch', 1800)
            for completed_task in asyncio.as_completed(tasks, timeout=stage3_timeout):
                try:
                    result = await completed_task
                    all_results.append(result)
                except Exception as e:
                    all_results.append(e)

                completed_count += 1
                progress.update_progress(completed=completed_count)

        except asyncio.TimeoutError:
            progress.log_message("⚠️ 阶段3任务超时，使用部分结果继续")
            # 安全取消未完成的任务
            await safe_cancel_tasks(tasks)
            # 使用已完成的结果
            if len(all_results) < len(tasks):
                all_results.extend([Exception("Timeout")] * (len(tasks) - len(all_results)))

        finally:
            # 确保所有任务都被清理
            await safe_cleanup_tasks(tasks)

        # 处理所有结果
        results = []
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ NER任务失败: {result}")
                failed_count += 1
            else:
                results.append(result)
                success_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段3完成 - 成功: {len(results)}/{len(test_data)}")

        # 清理不再需要的大型列表，释放内存
        del all_ner_tasks, tasks, all_results
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存NER结果到缓存
        try:
            await async_write_json(ner_cache_file, results)
            progress.log_message(f"💾 NER结果已缓存到: {ner_cache_file}")
        except Exception as e:
            progress.log_message(f"⚠️ NER缓存保存失败: {e}")

    # 计算汇总指标
    correct_predictions = sum(r['correct'] for r in results)
    total_entities = sum(r['total_true'] for r in results)
    predicted_entities = sum(r['total_predicted'] for r in results)
    
    print()

    # 计算最终指标
    precision = correct_predictions / predicted_entities if predicted_entities > 0 else 0
    recall = correct_predictions / total_entities if total_entities > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # 显示最终评估结果
    print("\n" + "="*60)
    print("🎯 最终评估结果")
    print("="*60)
    print(f"📊 数据集: {current_dataset['name']}")
    print(f"📝 处理样本数: {len(test_data)}")
    print(f"🎯 真实实体总数: {total_entities}")
    print(f"🔍 预测实体总数: {predicted_entities}")
    print(f"✅ 正确预测数: {correct_predictions}")
    print("-" * 40)
    print(f"📈 Precision: {precision:.4f} ({correct_predictions}/{predicted_entities})")
    print(f"📈 Recall: {recall:.4f} ({correct_predictions}/{total_entities})")
    print(f"📈 F1-Score: {f1_score:.4f}")
    print("="*60)
    
    # 保存评估结果
    eval_results = {
        'dataset': current_dataset['name'],
        'timestamp': datetime.now().isoformat(),
        'samples_count': len(test_data),
        'total_entities': total_entities,
        'predicted_entities': predicted_entities,
        'correct_predictions': correct_predictions,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'processing_mode': 'unified_three_stage',
        'detailed_results': results
    }
    
    # 保存到文件
    results_dir = CONFIG.get('results_dir', './results')
    os.makedirs(results_dir, exist_ok=True)
    eval_file = os.path.join(results_dir, f"eval_results_unified_{current_dataset['name'].lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    try:
        await async_write_json(eval_file, eval_results)
        print(f"💾 详细结果已保存到: {eval_file}")
    except Exception as e:
        print(f"⚠️ 保存结果失败: {e}")
    
    return eval_results


async def main():
    """主函数 - 三阶段统一处理"""
    parser = argparse.ArgumentParser(description='🧠 APIICL - 元认知智能体NER系统 (三阶段统一处理)')
    parser.add_argument('--dataset', '-d', type=str, default=CONFIG.get('dataset', 'ace2005'),
                       help=f'数据集名称 (默认: {CONFIG.get("dataset", "ace2005")})')
    parser.add_argument('--max-samples', type=int, default=CONFIG.get('max_test_samples'),
                       help=f'最大测试样本数 (默认: {CONFIG.get("max_test_samples", "处理全部")})')
    parser.add_argument('--log-level', type=str, default=CONFIG.get('log_level', 'WARNING'),
                       help=f'日志级别 (默认: {CONFIG.get("log_level", "WARNING")}) (DEBUG/INFO/WARNING/ERROR)')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)

    # 初始化数据集配置
    initialize_datasets()

    # 打印横幅
    print_banner()
    
    # 设置数据集
    if not set_dataset(args.dataset):
        print(f"❌ 数据集不存在: {args.dataset}")
        available = list_available_datasets()
        print("\n可用数据集:")
        for key, info in available.items():
            status = "✅" if info['available'] else "❌"
            current = "👈 当前" if info['current'] else ""
            print(f"  {status} {key}: {info['name']} {current}")
        return
    
    # 显示当前配置
    current_dataset = get_current_dataset_info()
    print(f"📊 数据集: {current_dataset['name']}")
    if args.max_samples:
        print(f"📝 最大样本数: {args.max_samples}")
    print()
    
    try:
        # 执行三阶段统一处理
        await process_and_eval_dataset(args.max_samples)
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
