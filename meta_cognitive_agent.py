#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 元认知智能体 - 基于单一超级Prompt的NER系统
核心理念：LLM一次性完成思考、决策和执行
遵循KISS原则：简单、高效、优雅
"""

import logging
from typing import Dict, List, Any, Type
from pydantic import BaseModel

from config import get_current_dataset_info
from model_interface import model_service
from schemas import RetrieveNERExamplesTool
from utils import parse_tool_arguments, robust_json_parse_ner

logger = logging.getLogger(__name__)


class MetaCognitiveAgent:
    """🧠 元认知智能体 - 单一超级Prompt架构的核心引擎"""
    
    def __init__(self, example_retriever=None):
        """
        初始化元认知智能体

        Args:
            example_retriever: 预初始化的示例检索器
        """
        if example_retriever is None:
            from example_retriever import example_retriever as default_retriever
            self.example_retriever = default_retriever
        else:
            self.example_retriever = example_retriever
        self.model_service = model_service
        self._initialization_started = False
        
    def _get_current_entity_types(self) -> List[str]:
        """获取当前数据集的实体类型"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])
    
    def _get_current_label_prompt(self) -> str:
        """获取当前数据集的标签提示"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('label_prompt', '')

    def _build_stage1_prompt(self, text: str) -> str:
        """
        {{ AURA-X: Optimize - 强化JSON格式要求. Approval: 寸止(ID:1738230400). }}
        构建Stage 1的prompt - 让LLM分析文本并生成检索请求
        """
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        return f"""Analyze this text and retrieve relevant NER examples to help with entity extraction.

Target entity types: {entity_types_str}
Text to analyze: "{text}"

Use the retrieve_ner_examples tool to find similar examples. Analyze the text across multiple dimensions:

1. **Description**: Provide a concise description of text characteristics (20-30 words)
2. **Entity Density**: Estimate entities per sentence (0-10 scale, e.g., 0-1=sparse, 2-3=medium, 4+=dense)
3. **Boundary Ambiguity**: Estimate entity boundary clarity (0-1 scale, 0=clear like 'Apple', 1=ambiguous like 'New York-based')
4. **Dependency Depth**: Estimate syntactic complexity (1-10 scale, 1-2=simple, 3-5=complex, 6+=very complex)

Focus on these dimensions to find the most relevant examples for this specific text."""

    async def _ensure_initialized(self):
        """确保示例检索器已初始化"""
        if not self._initialization_started and self.example_retriever and not self.example_retriever.initialized:
            self._initialization_started = True
            logger.info("🔧 自动初始化示例检索器...")
            await self.example_retriever.initialize_vector_store()

    def build_stage1_prompt(self, text: str) -> str:
        """
        公共方法：构建Stage 1的prompt
        供外部调用，避免直接访问私有方法
        """
        return self._build_stage1_prompt(text)

    async def simple_retrieval(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        公共方法：简化的检索方法
        供外部调用，避免直接访问私有方法
        """
        return await self._simple_retrieval(description, k)

    async def execute_ner_stage(self, text: str, few_shot_examples: List[Any]) -> Dict[str, List[str]]:
        """
        公共方法：执行NER阶段
        供外部调用，避免直接访问私有方法
        """
        return await self._execute_ner_stage(text, few_shot_examples)

    async def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        🚀 两阶段NER流程

        Stage 1: LLM接受input并做function call，用description和k调用检索器函数得到few-shot
        Stage 2: 用这些few-shot拼接成为prompt做NER
        """
        try:
            await self._ensure_initialized()

            logger.info(f"🧠 Stage 1: 分析文本并生成检索请求: '{text[:50]}...'")

            # Stage 1: LLM分析文本并生成检索请求
            stage1_prompt = self._build_stage1_prompt(text)
            tools: List[Type[BaseModel]] = [RetrieveNERExamplesTool]  # 只提供检索工具
            messages = [{"role": "user", "content": stage1_prompt}]

            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                # 执行检索获取few-shot
                few_shot_examples = await self._execute_retrieval_stage(response.tool_calls)

                if few_shot_examples:
                    logger.info(f"🧠 Stage 2: 基于{len(few_shot_examples)}个示例进行NER")
                    # Stage 2: 基于few-shot进行NER
                    return await self._execute_ner_stage(text, few_shot_examples)
                else:
                    logger.warning("❌ 未获取到few-shot示例")
                    return {}
            else:
                logger.warning("❌ LLM未调用检索工具")
                return {}

        except Exception as e:
            logger.error(f"两阶段NER失败: {e}")
            return {}

    async def _execute_retrieval_stage(self, tool_calls: List[Any]) -> List[Any]:
        """
        {{ AURA-X: Optimize - 添加跳过机制和多维度支持. Approval: 寸止(ID:1738230400). }}
        执行Stage 1的检索阶段 - 支持多维度参数但当前仍使用文本检索
        """
        for tool_call in tool_calls:
            if not tool_call.function or tool_call.function.name != "RetrieveNERExamplesTool":
                continue

            try:
                # 统一的JSON解析逻辑
                arguments = parse_tool_arguments(tool_call.function.arguments)
                if arguments is None:
                    logger.warning("参数解析失败，跳过此样本")
                    return []  # 直接跳过

                description = arguments.get("description", "")
                k = arguments.get("k", 3)

                # 提取多维度参数（当前仅记录，未来用于检索）
                dimensions = self._extract_dimensions(arguments)
                if dimensions:
                    logger.info(f"🧠 多维度分析: {dimensions}")

                # 执行检索（使用新的多维度接口，当前仍基于文本相似度）
                examples = await self.example_retriever.retrieve_with_dimensions(
                    query_text=description,
                    query_dimensions=dimensions,
                    k=k
                )
                logger.info(f"🔍 检索完成: {description[:30]}..., 返回{len(examples)}个示例")
                return examples

            except Exception as e:
                logger.error(f"检索阶段失败: {e}")
                return []  # 跳过失败的样本

        return []

    def _extract_dimensions(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取多维度特征参数
        当前仅用于日志记录，未来用于多维度检索
        """
        dimensions = {}

        if arguments.get("entity_density") is not None:
            dimensions["entity_density"] = arguments["entity_density"]

        if arguments.get("boundary_ambiguity") is not None:
            dimensions["boundary_ambiguity"] = arguments["boundary_ambiguity"]

        if arguments.get("dependency_depth") is not None:
            dimensions["dependency_depth"] = arguments["dependency_depth"]

        if arguments.get("additional_features"):
            dimensions.update(arguments["additional_features"])

        return dimensions

    async def _execute_ner_stage(self, text: str, few_shot_examples: List[Any]) -> Dict[str, List[str]]:
        """
        {{ AURA-X: Optimize - 强化prompt和解析. Approval: 寸止(ID:1738230400). }}
        执行Stage 2的NER阶段 - 强化prompt和多重解析
        """
        examples_text = self._format_examples_for_context(few_shot_examples)
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        ner_prompt = f"""You are an expert Named Entity Recognition system.

Extract named entities from text using ONLY the entity types specified below.

OUTPUT FORMAT REQUIREMENTS:
1. Return ONLY a valid JSON object
2. Keys must be entity types from the label set
3. Values must be arrays of entity strings
4. If no entities found for a type, use empty array []
5. If no entities found at all, return {{}}
6. NO explanations, NO additional text, ONLY JSON

Label set: {entity_types_str}

Examples (learn from these patterns):
{examples_text}

Text to analyze: "{text}"

JSON output:"""

        messages = [{"role": "user", "content": ner_prompt}]

        response = await self.model_service.generate_simple_async(
            messages=messages,
            temperature=0.0  # 使用0温度确保一致性
        )

        if response:
            entity_types = self._get_current_entity_types()
            return robust_json_parse_ner(response, entity_types)
        else:
            logger.warning("❌ Stage 2 NER失败")
            return {}

    async def _simple_retrieval(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        🔍 简化的检索方法 - 直接基于description检索k个示例
        """
        try:
            if not self.example_retriever or not self.example_retriever.initialized:
                logger.warning("⚠️ 示例检索器未初始化")
                return []

            # 直接使用description和k进行检索
            examples = await self.example_retriever.simple_retrieve(description, k)
            return examples

        except Exception as e:
            logger.error(f"简化检索失败: {e}")
            return []

    def _format_examples_for_context(self, examples) -> str:
        """
        {{ AURA-X: Optimize - 内联数据提取逻辑，减少函数调用. Approval: 寸止(ID:1738230400). }}
        优化的示例格式化方法
        """
        if not examples:
            return "No examples available."

        formatted_examples = []
        for i, example in enumerate(examples, 1):
            # 提取示例数据
            if hasattr(example, 'example'):
                example_data = example.example
            elif isinstance(example, dict):
                example_data = example
            else:
                example_data = {}
            if not example_data:
                continue

            text = example_data.get('text', '')
            labels = example_data.get('label', {})

            # 简化的实体格式化
            entities_str = self._format_entities(labels)
            formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")

        return "\n\n".join(formatted_examples)



    def _format_entities(self, labels: Dict[str, List[str]]) -> str:
        """
        简化的实体格式化
        """
        entities = []
        for etype, entities_list in labels.items():
            for entity in entities_list:
                entities.append(f"'{entity}' ({etype})")
        return ", ".join(entities)




# 🚀 全局实例管理
_meta_cognitive_agent = None

def get_meta_cognitive_agent(example_retriever=None):
    """获取元认知智能体实例"""
    global _meta_cognitive_agent
    if _meta_cognitive_agent is None:
        _meta_cognitive_agent = MetaCognitiveAgent(example_retriever)
    return _meta_cognitive_agent
