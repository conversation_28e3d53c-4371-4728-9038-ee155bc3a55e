"""
维度计算器 - 计算NER文本的多维度特征
"""

import re
import math
import logging
from typing import Dict, List, Any, Optional
from collections import Counter

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    spacy = None

logger = logging.getLogger(__name__)

class DimensionCalculator:
    """多维度特征计算器"""
    
    def __init__(self):
        """初始化计算器"""
        if SPACY_AVAILABLE:
            try:
                # 尝试加载spacy模型
                self.nlp = spacy.load("en_core_web_sm")
                self.spacy_available = True
                logger.info("✅ spaCy模型加载成功")
            except OSError as e:
                logger.warning(f"⚠️ spaCy模型未找到: {e}，使用简化计算方法")
                self.nlp = None
                self.spacy_available = False
        else:
            logger.warning("⚠️ spaCy未安装，使用简化计算方法")
            self.nlp = None
            self.spacy_available = False
    
    def calculate_entity_density(self, text: str, entities: List[Dict]) -> float:
        """
        计算实体密度：实体数量/句子数量
        
        Args:
            text: 原始文本
            entities: 实体列表，格式 [{'text': '...', 'label': '...', 'start': int, 'end': int}]
            
        Returns:
            float: 实体密度，范围0-10
        """
        if not text.strip():
            return 0.0
        
        # 计算句子数量
        sentences = self._count_sentences(text)
        if sentences == 0:
            return 0.0
        
        # 计算实体数量
        entity_count = len(entities) if entities else 0
        
        # 计算密度
        density = entity_count / sentences
        
        # 限制在0-10范围内
        return min(density, 10.0)
    
    def calculate_boundary_ambiguity(self, text: str, entities: List[Dict]) -> float:
        """
        计算实体边界模糊度
        
        Args:
            text: 原始文本
            entities: 实体列表
            
        Returns:
            float: 边界模糊度，范围0-1
        """
        if not entities:
            return 0.0
        
        ambiguity_score = 0.0
        total_entities = len(entities)
        
        for entity in entities:
            entity_text = entity.get('text', '')
            if not entity_text:
                continue
            
            score = 0.0
            
            # 检测复合词 (包含连字符、下划线)
            if '-' in entity_text or '_' in entity_text:
                score += 0.3
            
            # 检测多词实体
            if len(entity_text.split()) > 1:
                score += 0.2
            
            # 检测缩写 (全大写且长度2-5)
            if entity_text.isupper() and 2 <= len(entity_text) <= 5:
                score += 0.2
            
            # 检测数字混合
            if any(c.isdigit() for c in entity_text):
                score += 0.1
            
            # 检测特殊字符
            if any(c in entity_text for c in ['@', '#', '&', '.', ',']):
                score += 0.2
            
            # 检测括号或引号
            if any(c in entity_text for c in ['(', ')', '"', "'"]):
                score += 0.1
            
            ambiguity_score += min(score, 1.0)
        
        return min(ambiguity_score / total_entities, 1.0)
    
    def calculate_dependency_depth(self, text: str) -> int:
        """
        计算语法依赖深度
        
        Args:
            text: 原始文本
            
        Returns:
            int: 依赖深度，范围1-10
        """
        if not text.strip():
            return 1
        
        if self.spacy_available and self.nlp:
            return self._calculate_spacy_depth(text)
        else:
            return self._calculate_simple_depth(text)
    
    def _calculate_spacy_depth(self, text: str) -> int:
        """使用spaCy计算依赖深度"""
        try:
            doc = self.nlp(text)
            max_depth = 1
            
            for sent in doc.sents:
                for token in sent:
                    depth = self._get_token_depth(token)
                    max_depth = max(max_depth, depth)
            
            return min(max_depth, 10)
        except Exception as e:
            logger.warning(f"spaCy依赖分析失败: {e}")
            return self._calculate_simple_depth(text)
    
    def _get_token_depth(self, token, depth=1):
        """递归计算token的依赖深度"""
        if not list(token.children):
            return depth
        
        max_child_depth = depth
        for child in token.children:
            child_depth = self._get_token_depth(child, depth + 1)
            max_child_depth = max(max_child_depth, child_depth)
        
        return max_child_depth
    
    def _calculate_simple_depth(self, text: str) -> int:
        """简化的依赖深度计算（不使用spaCy）"""
        # 基于标点符号和句子结构的简单估计
        
        # 计算嵌套层次的指标
        depth_score = 1
        
        # 逗号数量（表示复杂句）
        comma_count = text.count(',')
        depth_score += min(comma_count * 0.3, 2)
        
        # 从句连接词
        subordinating_words = ['that', 'which', 'who', 'where', 'when', 'because', 'although', 'while', 'if']
        for word in subordinating_words:
            if f' {word} ' in text.lower():
                depth_score += 0.5
        
        # 括号嵌套
        paren_depth = 0
        max_paren_depth = 0
        for char in text:
            if char == '(':
                paren_depth += 1
                max_paren_depth = max(max_paren_depth, paren_depth)
            elif char == ')':
                paren_depth -= 1
        depth_score += max_paren_depth
        
        # 句子长度影响
        words = text.split()
        if len(words) > 20:
            depth_score += 1
        if len(words) > 40:
            depth_score += 1
        
        return min(int(depth_score), 10)
    
    def _count_sentences(self, text: str) -> int:
        """计算句子数量"""
        # 简单的句子分割
        sentences = re.split(r'[.!?]+', text.strip())
        # 过滤空句子
        sentences = [s.strip() for s in sentences if s.strip()]
        return max(len(sentences), 1)  # 至少1个句子
    
    def calculate_all_dimensions(self, text: str, entities: List[Dict]) -> Dict[str, float]:
        """
        计算所有维度特征
        
        Args:
            text: 原始文本
            entities: 实体列表
            
        Returns:
            Dict: 包含所有维度的字典
        """
        try:
            dimensions = {
                'entity_density': self.calculate_entity_density(text, entities),
                'boundary_ambiguity': self.calculate_boundary_ambiguity(text, entities),
                'dependency_depth': float(self.calculate_dependency_depth(text))
            }
            
            logger.debug(f"计算维度完成: {dimensions}")
            return dimensions
            
        except Exception as e:
            logger.error(f"维度计算失败: {e}")
            # 返回默认值
            return {
                'entity_density': 1.0,
                'boundary_ambiguity': 0.5,
                'dependency_depth': 2.0
            }
    
    def estimate_dimensions_from_text(self, text: str) -> Dict[str, float]:
        """
        仅从文本估计维度（无实体信息）
        用于Stage 1阶段的LLM分析验证
        
        Args:
            text: 原始文本
            
        Returns:
            Dict: 估计的维度值
        """
        try:
            # 估计实体密度（基于大写词、专有名词模式）
            words = text.split()
            sentences = self._count_sentences(text)
            
            # 简单的实体估计：大写开头的词、数字、特殊模式
            potential_entities = 0
            for word in words:
                if word[0].isupper() and len(word) > 1:  # 大写开头
                    potential_entities += 1
                elif re.match(r'\d+', word):  # 数字
                    potential_entities += 1
                elif '@' in word or '#' in word:  # 特殊符号
                    potential_entities += 1
            
            estimated_density = min(potential_entities / sentences, 10.0)
            
            # 估计边界模糊度（基于复杂词汇模式）
            complex_patterns = 0
            total_words = len(words)
            
            for word in words:
                if '-' in word or '_' in word:
                    complex_patterns += 1
                elif word.isupper() and 2 <= len(word) <= 5:
                    complex_patterns += 1
                elif any(c.isdigit() for c in word):
                    complex_patterns += 1
            
            estimated_ambiguity = min(complex_patterns / max(total_words, 1), 1.0)
            
            # 依赖深度使用现有方法
            estimated_depth = float(self.calculate_dependency_depth(text))
            
            return {
                'entity_density': estimated_density,
                'boundary_ambiguity': estimated_ambiguity,
                'dependency_depth': estimated_depth
            }
            
        except Exception as e:
            logger.error(f"文本维度估计失败: {e}")
            return {
                'entity_density': 1.0,
                'boundary_ambiguity': 0.5,
                'dependency_depth': 2.0
            }
