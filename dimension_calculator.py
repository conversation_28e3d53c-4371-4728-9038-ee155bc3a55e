"""
维度计算器 - 计算NER文本的多维度特征
"""

import re
import math
import logging
from typing import Dict, List, Any, Optional
from collections import Counter

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    spacy = None

logger = logging.getLogger(__name__)

class DimensionCalculator:
    """多维度特征计算器"""
    
    def __init__(self):
        """初始化计算器"""
        if SPACY_AVAILABLE:
            try:
                # 尝试加载spacy模型
                self.nlp = spacy.load("en_core_web_sm")
                self.spacy_available = True
                logger.info("✅ spaCy模型加载成功")
            except OSError as e:
                logger.warning(f"⚠️ spaCy模型未找到: {e}，使用简化计算方法")
                self.nlp = None
                self.spacy_available = False
        else:
            logger.warning("⚠️ spaCy未安装，使用简化计算方法")
            self.nlp = None
            self.spacy_available = False
    
    def calculate_entity_density(self, text: str, entities: List[Dict]) -> float:
        """
        计算实体密度：实体数量/句子数量
        
        Args:
            text: 原始文本
            entities: 实体列表，格式 [{'text': '...', 'label': '...', 'start': int, 'end': int}]
            
        Returns:
            float: 实体密度，范围0-10
        """
        if not text.strip():
            return 0.0
        
        # 计算句子数量
        sentences = self._count_sentences(text)
        if sentences == 0:
            return 0.0
        
        # 计算实体数量
        entity_count = len(entities) if entities else 0
        
        # 计算密度
        density = entity_count / sentences
        
        # 限制在0-10范围内
        return min(density, 10.0)
    
    def calculate_boundary_ambiguity(self, text: str, entities: List[Dict]) -> float:
        """
        计算实体边界模糊度
        
        Args:
            text: 原始文本
            entities: 实体列表
            
        Returns:
            float: 边界模糊度，范围0-1
        """
        if not entities:
            return 0.0
        
        ambiguity_score = 0.0
        total_entities = len(entities)
        
        for entity in entities:
            entity_text = entity.get('text', '')
            if not entity_text:
                continue
            
            score = 0.0
            
            # 检测复合词 (包含连字符、下划线)
            if '-' in entity_text or '_' in entity_text:
                score += 0.3
            
            # 检测多词实体
            if len(entity_text.split()) > 1:
                score += 0.2
            
            # 检测缩写 (全大写且长度2-5)
            if entity_text.isupper() and 2 <= len(entity_text) <= 5:
                score += 0.2
            
            # 检测数字混合
            if any(c.isdigit() for c in entity_text):
                score += 0.1
            
            # 检测特殊字符
            if any(c in entity_text for c in ['@', '#', '&', '.', ',']):
                score += 0.2
            
            # 检测括号或引号
            if any(c in entity_text for c in ['(', ')', '"', "'"]):
                score += 0.1
            
            ambiguity_score += min(score, 1.0)
        
        return min(ambiguity_score / total_entities, 1.0)
    
    def calculate_dependency_depth(self, text: str) -> int:
        """
        计算语法依赖深度
        
        Args:
            text: 原始文本
            
        Returns:
            int: 依赖深度，范围1-10
        """
        if not text.strip():
            return 1
        
        if self.spacy_available and self.nlp:
            return self._calculate_spacy_depth(text)
        else:
            return self._calculate_simple_depth(text)
    
    def _calculate_spacy_depth(self, text: str) -> int:
        """使用spaCy计算依赖深度"""
        try:
            doc = self.nlp(text)
            max_depth = 1
            
            for sent in doc.sents:
                for token in sent:
                    depth = self._get_token_depth(token)
                    max_depth = max(max_depth, depth)
            
            return min(max_depth, 10)
        except Exception as e:
            logger.warning(f"spaCy依赖分析失败: {e}")
            return self._calculate_simple_depth(text)
    
    def _get_token_depth(self, token, depth=1):
        """递归计算token的依赖深度"""
        if not list(token.children):
            return depth
        
        max_child_depth = depth
        for child in token.children:
            child_depth = self._get_token_depth(child, depth + 1)
            max_child_depth = max(max_child_depth, child_depth)
        
        return max_child_depth
    
    def _calculate_simple_depth(self, text: str) -> int:
        """简化的依赖深度计算（不使用spaCy）"""
        # 基于标点符号和句子结构的简单估计
        
        # 计算嵌套层次的指标
        depth_score = 1
        
        # 逗号数量（表示复杂句）
        comma_count = text.count(',')
        depth_score += min(comma_count * 0.3, 2)
        
        # 从句连接词
        subordinating_words = ['that', 'which', 'who', 'where', 'when', 'because', 'although', 'while', 'if']
        for word in subordinating_words:
            if f' {word} ' in text.lower():
                depth_score += 0.5
        
        # 括号嵌套
        paren_depth = 0
        max_paren_depth = 0
        for char in text:
            if char == '(':
                paren_depth += 1
                max_paren_depth = max(max_paren_depth, paren_depth)
            elif char == ')':
                paren_depth -= 1
        depth_score += max_paren_depth
        
        # 句子长度影响
        words = text.split()
        if len(words) > 20:
            depth_score += 1
        if len(words) > 40:
            depth_score += 1
        
        return min(int(depth_score), 10)
    
    def _count_sentences(self, text: str) -> int:
        """计算句子数量"""
        # 简单的句子分割
        sentences = re.split(r'[.!?]+', text.strip())
        # 过滤空句子
        sentences = [s.strip() for s in sentences if s.strip()]
        return max(len(sentences), 1)  # 至少1个句子
    
    def calculate_rhetorical_role(self, text: str) -> str:
        """
        计算修辞角色

        Args:
            text: 原始文本

        Returns:
            str: 修辞角色类型
        """
        # {{ AURA-X: Extend - 基于用户方案添加修辞角色分析. Approval: 寸止(ID:1738230404). }}
        # 简化的规则基础分析（可后续用LLM增强）
        text_lower = text.lower()

        # 检测陈述事实的模式
        if any(word in text_lower for word in ['said', 'reported', 'announced', 'stated', 'according to']):
            return '陈述事实'

        # 检测提出观点的模式
        if any(word in text_lower for word in ['believe', 'think', 'opinion', 'argue', 'suggest']):
            return '提出观点'

        # 检测举例说明的模式
        if any(word in text_lower for word in ['example', 'such as', 'for instance', 'including']):
            return '举例说明'

        # 检测反驳的模式
        if any(word in text_lower for word in ['however', 'but', 'although', 'despite', 'nevertheless']):
            return '进行反驳'

        # 默认为陈述事实
        return '陈述事实'

    def calculate_formality_level(self, text: str) -> float:
        """
        计算正式度

        Args:
            text: 原始文本

        Returns:
            float: 正式度评分，0-1范围
        """
        formality_score = 0.5  # 基础分数

        # 正式语言指标
        formal_indicators = ['furthermore', 'therefore', 'consequently', 'nevertheless', 'moreover']
        informal_indicators = ["don't", "can't", "won't", "it's", "that's", "i'm"]

        words = text.lower().split()
        total_words = len(words)

        if total_words == 0:
            return 0.5

        # 计算正式词汇比例
        formal_count = sum(1 for word in words if word in formal_indicators)
        informal_count = sum(1 for word in words if word in informal_indicators)

        # 调整分数
        formality_score += (formal_count / total_words) * 0.3
        formality_score -= (informal_count / total_words) * 0.3

        # 句子长度影响（长句通常更正式）
        avg_sentence_length = len(words) / max(self._count_sentences(text), 1)
        if avg_sentence_length > 15:
            formality_score += 0.1
        elif avg_sentence_length < 8:
            formality_score -= 0.1

        return max(0.0, min(1.0, formality_score))

    def calculate_information_density(self, text: str, entities: List[Dict]) -> float:
        """
        计算信息密度/惊奇度

        Args:
            text: 原始文本
            entities: 实体列表

        Returns:
            float: 信息密度评分，0-1范围
        """
        # 基于多个因素计算信息密度
        density_score = 0.0

        # 实体密度贡献
        entity_count = len(entities) if entities else 0
        words = text.split()
        if len(words) > 0:
            entity_ratio = entity_count / len(words)
            density_score += min(entity_ratio * 2, 0.3)  # 最多贡献0.3

        # 数字和特殊符号密度
        special_chars = sum(1 for char in text if char.isdigit() or char in '@#$%&*')
        if len(text) > 0:
            special_ratio = special_chars / len(text)
            density_score += min(special_ratio * 5, 0.2)  # 最多贡献0.2

        # 词汇多样性（TTR - Type-Token Ratio）
        unique_words = len(set(word.lower() for word in words))
        if len(words) > 0:
            ttr = unique_words / len(words)
            density_score += ttr * 0.3  # 最多贡献0.3

        # 句子复杂度贡献
        complexity = self.calculate_dependency_depth(text)
        density_score += min((complexity - 1) / 9 * 0.2, 0.2)  # 最多贡献0.2

        return max(0.0, min(1.0, density_score))

    def calculate_instruction_complexity(self, text: str, entities: List[Dict]) -> str:
        """
        计算指令复杂度

        Args:
            text: 原始文本
            entities: 实体列表

        Returns:
            str: 复杂度等级
        """
        complexity_score = 0

        # 实体数量影响
        entity_count = len(entities) if entities else 0
        if entity_count <= 2:
            complexity_score += 1
        elif entity_count <= 5:
            complexity_score += 2
        else:
            complexity_score += 3

        # 文本长度影响
        words = text.split()
        if len(words) <= 10:
            complexity_score += 1
        elif len(words) <= 30:
            complexity_score += 2
        else:
            complexity_score += 3

        # 语法复杂度影响
        dependency_depth = self.calculate_dependency_depth(text)
        if dependency_depth <= 2:
            complexity_score += 1
        elif dependency_depth <= 4:
            complexity_score += 2
        else:
            complexity_score += 3

        # 根据总分确定等级
        if complexity_score <= 4:
            return '低'
        elif complexity_score <= 7:
            return '中'
        else:
            return '高'

    def calculate_all_dimensions(self, text: str, entities: List[Dict]) -> Dict[str, Any]:
        """
        计算所有维度特征

        Args:
            text: 原始文本
            entities: 实体列表

        Returns:
            Dict: 包含所有维度的字典
        """
        try:
            # {{ AURA-X: Extend - 扩展维度计算，包含新的维度. Approval: 寸止(ID:1738230404). }}
            dimensions = {
                # 原有维度
                'entity_density': self.calculate_entity_density(text, entities),
                'boundary_ambiguity': self.calculate_boundary_ambiguity(text, entities),
                'dependency_depth': float(self.calculate_dependency_depth(text)),

                # 新增维度
                'rhetorical_role': self.calculate_rhetorical_role(text),
                'formality_level': self.calculate_formality_level(text),
                'information_density': self.calculate_information_density(text, entities),
                'instruction_complexity': self.calculate_instruction_complexity(text, entities)
            }

            logger.debug(f"计算维度完成: {dimensions}")
            return dimensions

        except Exception as e:
            logger.error(f"维度计算失败: {e}")
            # 返回默认值
            return {
                'entity_density': 1.0,
                'boundary_ambiguity': 0.5,
                'dependency_depth': 2.0,
                'rhetorical_role': '陈述事实',
                'formality_level': 0.5,
                'information_density': 0.5,
                'instruction_complexity': '中'
            }
    
    def estimate_dimensions_from_text(self, text: str) -> Dict[str, float]:
        """
        仅从文本估计维度（无实体信息）
        用于Stage 1阶段的LLM分析验证
        
        Args:
            text: 原始文本
            
        Returns:
            Dict: 估计的维度值
        """
        try:
            # 估计实体密度（基于大写词、专有名词模式）
            words = text.split()
            sentences = self._count_sentences(text)
            
            # 简单的实体估计：大写开头的词、数字、特殊模式
            potential_entities = 0
            for word in words:
                if word[0].isupper() and len(word) > 1:  # 大写开头
                    potential_entities += 1
                elif re.match(r'\d+', word):  # 数字
                    potential_entities += 1
                elif '@' in word or '#' in word:  # 特殊符号
                    potential_entities += 1
            
            estimated_density = min(potential_entities / sentences, 10.0)
            
            # 估计边界模糊度（基于复杂词汇模式）
            complex_patterns = 0
            total_words = len(words)
            
            for word in words:
                if '-' in word or '_' in word:
                    complex_patterns += 1
                elif word.isupper() and 2 <= len(word) <= 5:
                    complex_patterns += 1
                elif any(c.isdigit() for c in word):
                    complex_patterns += 1
            
            estimated_ambiguity = min(complex_patterns / max(total_words, 1), 1.0)
            
            # 依赖深度使用现有方法
            estimated_depth = float(self.calculate_dependency_depth(text))
            
            return {
                'entity_density': estimated_density,
                'boundary_ambiguity': estimated_ambiguity,
                'dependency_depth': estimated_depth
            }
            
        except Exception as e:
            logger.error(f"文本维度估计失败: {e}")
            return {
                'entity_density': 1.0,
                'boundary_ambiguity': 0.5,
                'dependency_depth': 2.0
            }
