from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from enum import Enum

# ====== 🚀 元认知智能体Function Calling工具 ======

class DomainType(str, Enum):
    """领域类型枚举"""
    NEWS = "news"
    MEDICAL = "medical"
    FINANCIAL = "financial"
    ACADEMIC = "academic"
    SOCIAL = "social"
    GENERAL = "general"

class RetrieveNERExamplesTool(BaseModel):
    """🧠 多维度NER示例检索工具 - 元认知驱动的ICL示例检索"""

    # 基础描述（保持兼容）
    description: str = Field(
        description="简洁描述文本的关键特征，重点关注：文本类型(新闻/对话/学术等)、领域(医疗/金融/通用等)、语言风格(正式/口语等)、实体复杂度。20-30个词以内。"
    )

    # {{ AURA-X: Extend - 基于用户方案扩展多维度特征. Approval: 寸止(ID:**********). }}
    # 核心维度（现有，保持兼容）
    entity_density: Optional[float] = Field(
        description="实体密度：预估文本中实体数量与句子数量的比值，范围0-10。例如：低密度0-1，中密度2-3，高密度4+",
        default=None,
        ge=0.0,
        le=10.0
    )

    boundary_ambiguity: Optional[float] = Field(
        description="实体边界模糊度：预估实体边界不清晰的程度，0-1范围。0=边界清晰(如'Apple')，1=高度模糊(如'New York-based company')",
        default=None,
        ge=0.0,
        le=1.0
    )

    dependency_depth: Optional[int] = Field(
        description="语法依赖深度：预估句子中依赖关系的最大层次深度，1-10范围。简单句=1-2，复杂句=3-5，极复杂=6+",
        default=None,
        ge=1,
        le=10
    )

    # 新增维度（基于用户方案）
    rhetorical_role: Optional[str] = Field(
        description="修辞角色：文本的修辞功能，如'陈述事实'、'提出观点'、'举例说明'、'进行反驳'",
        default=None
    )

    formality_level: Optional[float] = Field(
        description="正式度：语言的正式程度，0-1范围。0=非正式/口语化，1=高度正式/学术化",
        default=None,
        ge=0.0,
        le=1.0
    )

    information_density: Optional[float] = Field(
        description="信息密度：文本的信息量/惊奇度，0-1范围。0=常见/可预测，1=罕见/高信息量",
        default=None,
        ge=0.0,
        le=1.0
    )

    instruction_complexity: Optional[str] = Field(
        description="指令复杂度：任务的复杂程度，'低'、'中'、'高'",
        default=None
    )

    # 扩展维度预留
    additional_features: Optional[Dict[str, Any]] = Field(
        description="额外的维度特征，用于未来扩展新的分析维度",
        default=None
    )

    k: int = Field(
        description="需要检索的示例数量，推荐2-3个以获得更好的覆盖度",
        default=3,
        ge=1,
        le=5
    )

