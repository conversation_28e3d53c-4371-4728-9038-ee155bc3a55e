from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from enum import Enum

# ====== 🚀 元认知智能体Function Calling工具 ======

class DomainType(str, Enum):
    """领域类型枚举"""
    NEWS = "news"
    MEDICAL = "medical"
    FINANCIAL = "financial"
    ACADEMIC = "academic"
    SOCIAL = "social"
    GENERAL = "general"

class RetrieveNERExamplesTool(BaseModel):
    """🧠 多维度NER示例检索工具 - 分析文本特征并检索相关的NER示例"""

    # 基础描述（保持兼容）
    description: str = Field(
        description="简洁描述文本的关键特征，重点关注：文本类型(新闻/对话/学术等)、领域(医疗/金融/通用等)、语言风格(正式/口语等)、实体复杂度。20-30个词以内。"
    )

    # 多维度参数（可选，用于未来扩展）
    entity_density: Optional[float] = Field(
        description="实体密度：预估文本中实体数量与句子数量的比值，范围0-10。例如：低密度0-1，中密度2-3，高密度4+",
        default=None,
        ge=0.0,
        le=10.0
    )

    boundary_ambiguity: Optional[float] = Field(
        description="实体边界模糊度：预估实体边界不清晰的程度，0-1范围。0=边界清晰(如'Apple')，1=高度模糊(如'New York-based company')",
        default=None,
        ge=0.0,
        le=1.0
    )

    dependency_depth: Optional[int] = Field(
        description="语法依赖深度：预估句子中依赖关系的最大层次深度，1-10范围。简单句=1-2，复杂句=3-5，极复杂=6+",
        default=None,
        ge=1,
        le=10
    )

    # 扩展维度预留
    additional_features: Optional[Dict[str, Any]] = Field(
        description="额外的维度特征，用于未来扩展新的分析维度",
        default=None
    )

    k: int = Field(
        description="需要检索的示例数量，推荐2-3个以获得更好的覆盖度",
        default=3,
        ge=1,
        le=5
    )

